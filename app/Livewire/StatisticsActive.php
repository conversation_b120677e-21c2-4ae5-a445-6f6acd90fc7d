<?php

namespace App\Livewire;

use Exception;
use Illuminate\Support\Facades\Log;
use Livewire\Component;

class StatisticsActive extends Component
{
    public $pbtTotal = 0;
    public $pbtActive = 0;
    public $merchantTotal = 0;
    public $merchantActive = 0;
    public $newTotal = 0;
    public $newCategory = 0;
    public $totalCustomer = 0;
    public $heat_maps = [];
    public $year;
    public $errorMessage;
    protected $listeners = ['tab-changed' => 'handleTabChange'];
    public $tab;

    public function mount($tab = null) 
    {
        // $this->tab = $tab ?? date('Y'); // Default to current year if no tab is provided
        $this->tab = 'today';
        $this->fetchStatisticsActiveData($this->tab); // Pass $this->tab to avoid the error
    }

    public function handleTabChange($tab)
    {
        $this->tab = $tab;
        $this->fetchStatisticsActiveData($tab);
    }

    public function fetchStatisticsActiveData($tab)
    {
        try {
            $curl = curl_init();

            $headers = [
                'Content-Type: application/json',
                'Authorization: Bearer ' . session('authToken')
            ];

            // Add tab parameter to API request
            $url = env('API_URL') . '/statistic-active?tab=' . $tab;

            curl_setopt_array($curl, array(
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'GET',
                CURLOPT_HTTPHEADER => $headers,
            ));

            $response = curl_exec($curl);
            curl_close($curl);
            
            // HTTP status code
            $http_status = curl_getinfo($curl, CURLINFO_HTTP_CODE);

            // Check status code
            if ($http_status == 200) {

                $result = json_decode($response); // Assume response in JSON format

                if ($result->status == "1") {

                    $this->pbtTotal = $result->pbtTotal ?? 0;
                    $this->pbtActive = $result->pbtActive ?? 0;
                    $this->merchantTotal = $result->merchantTotal ?? 0;
                    $this->merchantActive = $result->merchantActive ?? 0;
                    $this->newTotal = $result->newTotal ?? 0;
                    $this->newCategory = $result->newCategory ?? 0;
                    $this->totalCustomer = $result->totalCustomer ?? 0;
                    $this->heat_maps = $result->heat_maps ?? [];

                } else {
                    throw new Exception('Unable to fetch statistics active data');
                }
            } else {
                Log::info('Unable to reach backend server ' . $http_status . ' - Using fallback data');

                // Fallback data when API is not available
                $this->pbtTotal = 12;
                $this->pbtActive = 0;
                $this->merchantTotal = 0;
                $this->merchantActive = 0;
                $this->newTotal = 0;
                $this->newCategory = 0;
                $this->totalCustomer = 0;
                $this->heat_maps = [];

                return; // Exit early with fallback data
            }
        } catch (Exception $e) {
            $this->errorMessage = $e->getMessage();
        }
    }

    public function render()
    {
        return view('livewire.statistics-active');
    }
}
